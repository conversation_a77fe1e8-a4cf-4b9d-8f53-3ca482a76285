# App Hub Repository

This directory contains the App Hub repository data for Je<PERSON>'s Raspberry Pi
Management app.

## Structure

- `repository.json` \- Main repository file containing application definitions
- `icons/` \- Application icons (future enhancement)

## Repository Format

The repository.json file contains:

### Repository Information

- `name` \- Repository name
- `version` \- Repository version
- `description` \- Repository description
- `last_updated` \- Last update timestamp

### Categories

Each category has:

- `id` \- Unique identifier
- `name` \- Display name
- `description` \- Category description
- `icon` \- Icon name (Material Icons)

### Applications

Each application has:

- `id` \- Unique identifier
- `name` \- Display name
- `description` \- Application description
- `version` \- Version string
- `category` \- Category ID
- `icon` \- Icon filename
- `size` \- Estimated download size
- `install_type` \- Installation method (apt, script, deb, docker)
- `install_commands` \- Array of shell commands to install
- `remove_commands` \- Array of shell commands to remove
- `check_installed` \- Command to check if installed
- `tags` \- Search tags
- `requires_reboot` \- Whether reboot is needed after install
- `pi_only` \- Whether app is Raspberry Pi specific
- `dependencies` \- Array of dependency app IDs

## Adding New Applications

To add a new application:

1.  Add the application definition to the `applications` array in `
    repository.json`
2.  Ensure all required fields are filled
3.  Test installation commands on a real Raspberry Pi
4.  Add appropriate tags for searchability

## Installation Types

- `apt` \- Standard APT package installation
- `script` \- Custom script installation (like Pi-hole)
- `deb` \- Direct .deb package installation
- `docker` \- Docker container installation

## Future Enhancements

- Remote repository hosting
- Automatic updates
- User-contributed applications
- Application ratings and reviews
- Dependency resolution improvements
- Application icons
